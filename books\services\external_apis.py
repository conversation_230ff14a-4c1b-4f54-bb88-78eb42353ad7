"""
External APIs service for retrieving book information from multiple sources.
Integrates Google Books, Gutendx, Internet Archive, and Arabic Collections Online.
"""

import requests
import urllib.parse
from typing import Dict, List, Optional
from bs4 import BeautifulSoup
import json
import re
import re
from .llm_service import LLMService


class ExternalAPIsService:
    """Service for integrating multiple external book APIs and sources."""

    def __init__(self):
        self.google_books_api = "https://www.googleapis.com/books/v1/volumes"
        self.gutendx_api = "https://gutendx.com/books"
        self.internet_archive_api = "https://archive.org/advancedsearch.php"
        self.aco_search_url = "https://dlib.nyu.edu/aco/search/"
        self.libgen_search_url = "https://libgen.is/search.php"
        self.pdfdrive_search_url = "https://www.pdfdrive.com/search"

        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }

        # Additional PDF sources
        self.additional_pdf_sources = [
            "https://archive.org/details/",
            "https://www.gutenberg.org/ebooks/",
            "https://manybooks.net/search-book?search="
        ]
    
    def search_all_sources(self, extracted_info: Dict, max_results: int = 5) -> List[Dict]:
        """
        Search all available sources for book information.
        
        Args:
            extracted_info: Information extracted by LLM
            max_results: Maximum number of results to return
            
        Returns:
            List of book results from all sources
        """
        
        all_results = []
        title = extracted_info.get('title', '')
        author = extracted_info.get('author', '')
        search_variations = extracted_info.get('search_variations', [title])
        is_arabic_query = extracted_info.get('is_arabic_query', False)
        
        # If it's an Arabic query, prioritize Arabic sources
        if is_arabic_query:
            # Search Arabic Collections Online first
            for variation in search_variations:
                aco_results = self.search_arabic_collections_online(variation)
                all_results.extend(aco_results)
                if len(all_results) >= max_results:
                    break
            
            # Then search other sources
            for variation in search_variations:
                if len(all_results) < max_results:
                    google_results = self.search_google_books(variation, prefer_arabic=True)
                    all_results.extend(google_results)
                
                if len(all_results) < max_results:
                    ia_results = self.search_internet_archive(variation, prefer_arabic=True)
                    all_results.extend(ia_results)
                
                if len(all_results) < max_results:
                    gutendx_results = self.search_gutendx(variation)
                    all_results.extend(gutendx_results)
        else:
            # For English queries, search in order of reliability
            for variation in search_variations:
                if len(all_results) < max_results:
                    google_results = self.search_google_books(variation)
                    all_results.extend(google_results)
                
                if len(all_results) < max_results:
                    gutendx_results = self.search_gutendx(variation)
                    all_results.extend(gutendx_results)
                
                if len(all_results) < max_results:
                    ia_results = self.search_internet_archive(variation)
                    all_results.extend(ia_results)
        
        # Remove duplicates and rank results
        unique_results = self._remove_duplicates(all_results)
        ranked_results = self._rank_results(unique_results, extracted_info)

        # Enhance PDF URLs for results that don't have them
        enhanced_results = self._enhance_pdf_urls(ranked_results[:max_results])

        return enhanced_results
    
    def search_google_books(self, query: str, prefer_arabic: bool = False) -> List[Dict]:
        """Search Google Books API."""
        try:
            # Add language preference to query
            if prefer_arabic:
                search_query = f"{query} language:ar"
            else:
                search_query = query
            
            params = {
                'q': search_query,
                'maxResults': 10,
                'printType': 'books'
            }
            
            response = requests.get(self.google_books_api, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            results = []
            for item in data.get('items', []):
                volume_info = item.get('volumeInfo', {})
                access_info = item.get('accessInfo', {})
                
                # Extract PDF URL - try multiple sources
                pdf_url = None

                # Check if PDF is directly available
                if access_info.get('pdf', {}).get('isAvailable'):
                    pdf_url = access_info.get('pdf', {}).get('downloadLink')

                # If no PDF, check for EPUB (can be converted)
                if not pdf_url and access_info.get('epub', {}).get('isAvailable'):
                    pdf_url = access_info.get('epub', {}).get('downloadLink')

                # Check for viewability and public domain status
                viewability = access_info.get('viewability', '')
                if not pdf_url and viewability in ['ALL_PAGES', 'PARTIAL'] and access_info.get('publicDomain', False):
                    # For public domain books, construct a potential PDF URL
                    book_id = item.get('id')
                    if book_id:
                        pdf_url = f"https://books.google.com/books/download/id{book_id}.pdf"
                
                # Extract cover image
                cover_url = None
                image_links = volume_info.get('imageLinks', {})
                if image_links:
                    cover_url = (image_links.get('large') or 
                               image_links.get('medium') or 
                               image_links.get('small') or 
                               image_links.get('thumbnail'))
                
                result = {
                    'title': volume_info.get('title', ''),
                    'author': ', '.join(volume_info.get('authors', [])),
                    'description': volume_info.get('description', ''),
                    'categories': volume_info.get('categories', []),
                    'cover_image_url': cover_url,
                    'pdf_url': pdf_url,
                    'pdf_source': 'google_books',
                    'isbn': self._extract_isbn(volume_info.get('industryIdentifiers', [])),
                    'publication_date': volume_info.get('publishedDate', ''),
                    'publisher': volume_info.get('publisher', ''),
                    'language': volume_info.get('language', 'en'),
                    'source_api': 'google_books',
                    'external_id': item.get('id'),
                    'relevance_score': 0.8
                }
                
                if result['title']:  # Only add if we have a title
                    results.append(result)
            
            return results
            
        except Exception as e:
            print(f"Google Books API error: {e}")
            return []
    
    def search_gutendx(self, query: str) -> List[Dict]:
        """Search Project Gutenberg via Gutendx API."""
        try:
            # First try with PDF mime type
            params = {
                'search': query,
                'mime_type': 'application/pdf'
            }

            response = requests.get(self.gutendx_api, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            results = []
            for book in data.get('results', []):
                # Extract PDF URL - try multiple format types
                pdf_url = None
                formats = book.get('formats', {})

                # Look for PDF formats in order of preference
                for format_url, format_type in formats.items():
                    if any(pdf_type in format_type.lower() for pdf_type in ['pdf', 'application/pdf']):
                        pdf_url = format_url
                        break

                # If no PDF found, try EPUB (can be converted)
                if not pdf_url:
                    for format_url, format_type in formats.items():
                        if 'epub' in format_type.lower():
                            pdf_url = format_url
                            break
                
                # Extract author
                authors = []
                for person in book.get('authors', []):
                    authors.append(person.get('name', ''))
                
                # Extract subjects as categories
                categories = book.get('subjects', [])
                
                result = {
                    'title': book.get('title', ''),
                    'author': ', '.join(authors),
                    'description': f"Public domain book from Project Gutenberg. Download count: {book.get('download_count', 0)}",
                    'categories': categories,
                    'cover_image_url': None,  # Gutenberg doesn't provide covers
                    'pdf_url': pdf_url,
                    'pdf_source': 'gutendx',
                    'isbn': None,
                    'publication_date': '',
                    'publisher': 'Project Gutenberg',
                    'language': ', '.join(book.get('languages', ['en'])),
                    'source_api': 'gutendx',
                    'external_id': str(book.get('id')),
                    'relevance_score': 0.9 if pdf_url else 0.5
                }
                
                if result['title']:
                    results.append(result)
            
            return results
            
        except Exception as e:
            print(f"Gutendx API error: {e}")
            return []
    
    def search_internet_archive(self, query: str, prefer_arabic: bool = False) -> List[Dict]:
        """Search Internet Archive."""
        try:
            # Build search query
            if prefer_arabic:
                search_query = f"({query}) AND mediatype:texts AND language:Arabic"
            else:
                search_query = f"({query}) AND mediatype:texts"
            
            params = {
                'q': search_query,
                'fl': 'identifier,title,creator,description,subject,date,language,format',
                'rows': 10,
                'page': 1,
                'output': 'json'
            }
            
            response = requests.get(self.internet_archive_api, params=params, timeout=15)
            response.raise_for_status()
            data = response.json()
            
            results = []
            for doc in data.get('response', {}).get('docs', []):
                identifier = doc.get('identifier')
                if not identifier:
                    continue
                
                # Check if PDF is available and get the correct PDF URL
                pdf_url = None
                formats = doc.get('format', [])
                if isinstance(formats, str):
                    formats = [formats]

                if 'PDF' in formats or 'Abbyy GZ' in formats:
                    # Try to get the actual PDF filename from Internet Archive
                    pdf_url = self._get_internet_archive_pdf_url(identifier)
                
                # Extract subjects as categories
                subjects = doc.get('subject', [])
                if isinstance(subjects, str):
                    subjects = [subjects]
                
                result = {
                    'title': doc.get('title', ''),
                    'author': doc.get('creator', ''),
                    'description': doc.get('description', ''),
                    'categories': subjects,
                    'cover_image_url': f"https://archive.org/services/img/{identifier}",
                    'pdf_url': pdf_url,
                    'pdf_source': 'internet_archive',
                    'isbn': None,
                    'publication_date': doc.get('date', ''),
                    'publisher': 'Internet Archive',
                    'language': doc.get('language', 'en'),
                    'source_api': 'internet_archive',
                    'external_id': identifier,
                    'relevance_score': 0.7 if pdf_url else 0.3
                }
                
                if result['title']:
                    results.append(result)
            
            return results
            
        except Exception as e:
            print(f"Internet Archive API error: {e}")
            return []
    
    def search_arabic_collections_online(self, query: str) -> List[Dict]:
        """Search Arabic Collections Online (ACO)."""
        try:
            search_url = f"{self.aco_search_url}?q={urllib.parse.quote(query)}&scope=containsAny"
            
            response = requests.get(search_url, headers=self.headers, timeout=15)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, 'html.parser')
            
            results = []
            
            # Look for search result items
            # ACO has a specific structure - we need to find the right selectors
            result_items = soup.find_all('div', class_='search-result-item') or soup.find_all('div', class_='item')
            
            for item in result_items:
                title = ''
                author = ''
                pdf_url = None
                
                # Extract title
                title_elem = (item.find('h3') or 
                             item.find('h2') or 
                             item.find('a', class_='title') or
                             item.find('strong'))
                if title_elem:
                    title = title_elem.get_text(strip=True)
                
                # Extract author
                author_elem = (item.find('p', class_='author') or
                              item.find('span', class_='author') or
                              item.find('div', class_='author'))
                if author_elem:
                    author = author_elem.get_text(strip=True)
                
                # Look for PDF download links
                pdf_links = item.find_all('a', href=True)
                for link in pdf_links:
                    href = link.get('href', '')
                    link_text = link.get_text(strip=True).lower()
                    
                    if ('pdf' in link_text or 'تحميل' in link_text or 'download' in link_text):
                        if href.startswith('http'):
                            pdf_url = href
                        elif href.startswith('/'):
                            pdf_url = f"https://dlib.nyu.edu{href}"
                        break
                
                if title:
                    result = {
                        'title': title,
                        'author': author,
                        'description': f"Arabic book from Arabic Collections Online",
                        'categories': ['Arabic Literature'],
                        'cover_image_url': None,
                        'pdf_url': pdf_url,
                        'pdf_source': 'aco',
                        'isbn': None,
                        'publication_date': '',
                        'publisher': 'Arabic Collections Online',
                        'language': 'ar',
                        'source_api': 'aco',
                        'external_id': None,
                        'relevance_score': 0.9 if pdf_url else 0.6
                    }
                    results.append(result)
            
            return results
            
        except Exception as e:
            print(f"Arabic Collections Online error: {e}")
            return []
    
    def _extract_isbn(self, identifiers: List[Dict]) -> Optional[str]:
        """Extract ISBN from Google Books identifiers."""
        for identifier in identifiers:
            if identifier.get('type') in ['ISBN_13', 'ISBN_10']:
                return identifier.get('identifier')
        return None
    
    def _remove_duplicates(self, results: List[Dict]) -> List[Dict]:
        """Remove duplicate results based on title and author similarity."""
        unique_results = []
        seen_combinations = set()
        
        for result in results:
            # Create a normalized key for comparison
            title_key = result.get('title', '').lower().strip()
            author_key = result.get('author', '').lower().strip()
            combination_key = f"{title_key}|{author_key}"
            
            if combination_key not in seen_combinations:
                seen_combinations.add(combination_key)
                unique_results.append(result)
        
        return unique_results
    
    def _rank_results(self, results: List[Dict], extracted_info: Dict) -> List[Dict]:
        """Rank results based on relevance to the original query."""
        target_title = extracted_info.get('title', '').lower()
        target_author = extracted_info.get('author', '').lower() if extracted_info.get('author') else ''
        is_arabic_query = extracted_info.get('is_arabic_query', False)
        
        for result in results:
            score = result.get('relevance_score', 0.5)
            
            # Boost score for title match
            result_title = result.get('title', '').lower()
            if target_title in result_title or result_title in target_title:
                score += 0.3
            
            # Boost score for author match
            result_author = result.get('author', '').lower()
            if target_author and (target_author in result_author or result_author in target_author):
                score += 0.2
            
            # Boost score for Arabic books if Arabic query
            if is_arabic_query and result.get('language') == 'ar':
                score += 0.2
            
            # Boost score for books with PDF
            if result.get('pdf_url'):
                score += 0.1
            
            result['relevance_score'] = min(score, 1.0)  # Cap at 1.0
        
        # Sort by relevance score
        return sorted(results, key=lambda x: x.get('relevance_score', 0), reverse=True)

    def _enhance_pdf_urls(self, results: List[Dict]) -> List[Dict]:
        """Enhance results by finding PDF URLs for books that don't have them."""
        # Import here to avoid circular imports
        from .llm_service import LLMService

        # Initialize LLM service
        try:
            llm_service = LLMService()
            enhanced_results = []

            for result in results:
                # If no PDF URL found, try to find one using LLM
                if not result.get('pdf_url'):
                    title = result.get('title', '')
                    author = result.get('author', '')
                    language = result.get('language', 'en')

                    # Use LLM to find PDF link
                    pdf_url = llm_service.find_pdf_link(title, author, language)

                    # Validate the PDF URL
                    if pdf_url and self._is_valid_pdf_url(pdf_url):
                        result['pdf_url'] = pdf_url
                        result['pdf_source'] = 'llm_enhanced'
                        result['relevance_score'] += 0.1  # Boost score for having PDF

                    # Also try additional search methods
                    if not result.get('pdf_url'):
                        pdf_url = self._search_additional_pdf_sources(title, author)
                        if pdf_url:
                            result['pdf_url'] = pdf_url
                            result['pdf_source'] = 'additional_search'
                            result['relevance_score'] += 0.1

                enhanced_results.append(result)

            return enhanced_results

        except Exception as e:
            print(f"Error enhancing PDF URLs: {e}")
            # Return original results if enhancement fails
            return results

    def _get_internet_archive_pdf_url(self, identifier: str) -> Optional[str]:
        """Get the actual PDF URL from Internet Archive item details."""
        try:
            # Get item metadata
            metadata_url = f"https://archive.org/metadata/{identifier}"
            response = requests.get(metadata_url, timeout=10)
            response.raise_for_status()
            metadata = response.json()

            # Look for PDF files in the files list
            files = metadata.get('files', [])

            # First, look for files with PDF format
            for file_info in files:
                filename = file_info.get('name', '')
                format_type = file_info.get('format', '')

                if format_type == 'PDF' and filename.lower().endswith('.pdf'):
                    return f"https://archive.org/download/{identifier}/{filename}"

            # Second, look for any file ending with .pdf
            for file_info in files:
                filename = file_info.get('name', '')
                if filename.lower().endswith('.pdf'):
                    return f"https://archive.org/download/{identifier}/{filename}"

            # Third, look for PDF derivatives
            for file_info in files:
                filename = file_info.get('name', '')
                if 'pdf' in filename.lower() and not filename.lower().endswith('.xml'):
                    return f"https://archive.org/download/{identifier}/{filename}"

            # Fallback to standard naming
            standard_pdf = f"https://archive.org/download/{identifier}/{identifier}.pdf"

            # Verify if the standard PDF exists
            try:
                head_response = requests.head(standard_pdf, timeout=5)
                if head_response.status_code == 200:
                    return standard_pdf
            except:
                pass

            return None

        except Exception as e:
            print(f"Error getting Internet Archive PDF URL: {e}")
            return None

    def _search_additional_pdf_sources(self, title: str, author: str) -> Optional[str]:
        """Search additional sources for PDF links."""
        try:
            # Try searching ManyBooks
            search_query = f"{title} {author}".strip()
            manybooks_url = f"https://manybooks.net/search-book?search={urllib.parse.quote(search_query)}"

            response = requests.get(manybooks_url, headers=self.headers, timeout=10)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')

                # Look for PDF download links
                pdf_links = soup.find_all('a', href=True)
                for link in pdf_links:
                    href = link.get('href', '')
                    if 'pdf' in href.lower() and ('download' in href.lower() or 'get' in href.lower()):
                        if href.startswith('http'):
                            return href
                        elif href.startswith('/'):
                            return f"https://manybooks.net{href}"

            return None

        except Exception as e:
            print(f"Error searching additional PDF sources: {e}")
            return None

