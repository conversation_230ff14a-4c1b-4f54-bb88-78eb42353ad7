# AI-Powered Book Search API - Quick Start Guide

This guide will help you quickly set up and start using the AI-Powered Book Search API.

## 1. Setup

### Prerequisites
- Python 3.8 or higher
- Groq API key (get from https://console.groq.com/)

### Installation

#### Automatic Setup (Recommended)
Run the setup script:
```bash
python setup.py
```

#### Manual Setup
1. Create a virtual environment:
```bash
# Windows
python -m venv venv
venv\Scripts\activate

# macOS/Linux
python3 -m venv venv
source venv/bin/activate
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Set up the database:
```bash
python manage.py makemigrations
python manage.py migrate
```

## 2. Configuration

### Set Groq API Key
Edit `book_api_project/settings.py` and update:
```python
GROQ_API_KEY = 'your_groq_api_key_here'
```

## 3. Run the Server
```bash
python manage.py runserver
```

The API will be available at: `http://localhost:8000/api/books/`

## 4. Test the API

### Using the Test Script
Run the automated test script:
```bash
python test_api.py
```

### Manual Testing

#### Search for Books
```bash
curl -X POST http://localhost:8000/api/books/ai-search/ \
  -H "Content-Type: application/json" \
  -d '{
    "book_name": "The Great Gatsby",
    "language": "en",
    "max_results": 3
  }'
```

#### Verify PDF Link
```bash
curl -X POST http://localhost:8000/api/books/verify-pdf/ \
  -H "Content-Type: application/json" \
  -d '{
    "pdf_url": "https://archive.org/download/the-great-gatsby-1st-ed-1925/The_Great_Gatsby-1stEd-1925.pdf"
  }'
```

## 5. API Endpoints

### AI Book Search
**POST** `/api/books/ai-search/`

**Request:**
```json
{
    "book_name": "Pride and Prejudice",
    "language": "en",
    "max_results": 5
}
```

**Response:**
```json
{
    "search_session": "uuid-string",
    "results": [
        {
            "id": 1,
            "title": "Pride and Prejudice",
            "author": "Jane Austen",
            "description": "A classic novel...",
            "pdf_url": "https://archive.org/download/book.pdf",
            "pdf_verified": true,
            "pdf_verified_status": "verified",
            "relevance_score": 1.3
        }
    ],
    "total_found": 1
}
```

### Add Book from Search
**POST** `/api/books/add-from-search/`

**Request:**
```json
{
    "search_result_id": 1,
    "status": "published",
    "custom_category": "Classic Literature",
    "download_pdf": true
}
```

### Get Search Results
**GET** `/api/books/search-results/{search_session}/`

### Verify PDF Link
**POST** `/api/books/verify-pdf/`

**Request:**
```json
{
    "pdf_url": "https://example.com/book.pdf"
}
```

### List Books
**GET** `/api/books/`

### Get Book Details
**GET** `/api/books/{book_id}/`

## 6. Troubleshooting

### Common Issues

1. **Server won't start**
   - Check if port 8000 is already in use
   - Try a different port: `python manage.py runserver 8001`

2. **API Key Error**
   - Ensure your Groq API key is valid and properly set
   - Check API rate limits

3. **PDF Verification Timeouts**
   - Some PDF links may timeout during verification
   - The system will try multiple sources automatically

4. **No PDF Links Found**
   - Try different search terms
   - Try a different language
   - Some books may not have available PDFs

## 7. Next Steps

- Read the full documentation in `README.md`
- Explore the codebase to understand how it works
- Customize the API for your specific needs

For detailed documentation, see `README.md`.
