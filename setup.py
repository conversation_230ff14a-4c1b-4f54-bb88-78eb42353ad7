#!/usr/bin/env python
"""
Setup script for AI-Powered Book Search API
"""

import os
import sys
import subprocess
import platform

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python version {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def create_virtual_environment():
    """Create and activate virtual environment."""
    if platform.system() == "Windows":
        venv_activate = "venv\\Scripts\\activate"
        create_cmd = "python -m venv venv"
    else:
        venv_activate = "source venv/bin/activate"
        create_cmd = "python3 -m venv venv"
    
    if not os.path.exists("venv"):
        if not run_command(create_cmd, "Creating virtual environment"):
            return False
    else:
        print("✅ Virtual environment already exists")
    
    return True

def install_requirements():
    """Install required packages."""
    if platform.system() == "Windows":
        pip_cmd = "venv\\Scripts\\pip install -r requirements.txt"
    else:
        pip_cmd = "venv/bin/pip install -r requirements.txt"
    
    return run_command(pip_cmd, "Installing requirements")

def setup_database():
    """Setup Django database."""
    if platform.system() == "Windows":
        python_cmd = "venv\\Scripts\\python"
    else:
        python_cmd = "venv/bin/python"
    
    commands = [
        (f"{python_cmd} manage.py makemigrations", "Creating database migrations"),
        (f"{python_cmd} manage.py migrate", "Applying database migrations")
    ]
    
    for cmd, desc in commands:
        if not run_command(cmd, desc):
            return False
    return True

def check_groq_api_key():
    """Check if Groq API key is configured."""
    try:
        from book_api_project.settings import GROQ_API_KEY
        if GROQ_API_KEY and GROQ_API_KEY != 'your_groq_api_key_here':
            print("✅ Groq API key is configured")
            return True
        else:
            print("⚠️  Groq API key not configured")
            print("Please update GROQ_API_KEY in book_api_project/settings.py")
            print("Get your API key from: https://console.groq.com/")
            return False
    except ImportError:
        print("❌ Cannot check Groq API key - Django not properly installed")
        return False

def main():
    """Main setup function."""
    print("🚀 Setting up AI-Powered Book Search API")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create virtual environment
    if not create_virtual_environment():
        print("❌ Failed to create virtual environment")
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        print("❌ Failed to install requirements")
        sys.exit(1)
    
    # Setup database
    if not setup_database():
        print("❌ Failed to setup database")
        sys.exit(1)
    
    # Check API key
    check_groq_api_key()
    
    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Configure your Groq API key in book_api_project/settings.py")
    print("2. Activate virtual environment:")
    
    if platform.system() == "Windows":
        print("   venv\\Scripts\\activate")
        print("3. Run the server: python manage.py runserver")
    else:
        print("   source venv/bin/activate")
        print("3. Run the server: python manage.py runserver")
    
    print("4. Access the API at: http://localhost:8000/api/books/")
    print("\nFor detailed documentation, see README.md")

if __name__ == "__main__":
    main()
